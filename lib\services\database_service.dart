import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import 'package:logger/logger.dart';
import 'package:path/path.dart';
import 'package:synchronized/synchronized.dart';
import 'package:tubewell_water_billing/models/customer.dart';
import 'package:tubewell_water_billing/models/bill.dart';
import 'package:tubewell_water_billing/models/payment.dart';
import 'package:tubewell_water_billing/models/customer_credit.dart';
import 'package:tubewell_water_billing/models/payment_allocation.dart';
import 'package:tubewell_water_billing/models/expense.dart';
import 'package:tubewell_water_billing/services/account_service.dart';

/// A service for interacting with the SQLite database
class DatabaseService {
  static final Logger _logger = Logger();
  static bool isInitialized = false;
  static Database? _database;
  static String? _currentAccountId;

  // Singleton instance
  static final DatabaseService _instance = DatabaseService._internal();

  // Factory constructor
  factory DatabaseService() => _instance;

  // Internal constructor
  DatabaseService._internal();

  // Getter for the instance
  static DatabaseService get instance => _instance;

  // Getter for the database
  static Future<Database> get database async {
    // Use the lock to prevent concurrent initialization
    return _initLock.synchronized(() async {
      if (_database != null) return _database!;

      // If not initialized, initialize the database
      if (!isInitialized) {
        _logger.i('Database not initialized, initializing now');
        await initialize(accountId: _currentAccountId);
      }

      // Double-check that database is available after initialization
      if (_database == null) {
        _logger.e('Database is null after initialization');
        throw Exception('Failed to initialize database');
      }

      return _database!;
    });
  }

  // Get the database path for backup/restore
  static Future<String> getDatabasePath() async {
    if (!isInitialized) await initialize();

    if (_database == null) {
      throw Exception('SQLite database not initialized');
    }

    final dir = await getApplicationDocumentsDirectory();
    final dbName = _currentAccountId != null
        ? 'account_$_currentAccountId.db'
        : 'default.db';
    return join(dir.path, dbName);
  }

  // Get the database path for a specific account ID
  static Future<String> getDatabasePathForAccount(String accountId) async {
    final dir = await getApplicationDocumentsDirectory();
    return join(dir.path, 'account_$accountId.db');
  }

  // Close the database connection
  static Future<void> closeDatabase() async {
    // Use the same lock as initialize to prevent race conditions
    return _initLock.synchronized(() async {
      if (_database != null) {
        try {
          _logger.i('Closing database connection');
          await _database!.close();
          _logger.i('Database connection closed successfully');
        } catch (e) {
          _logger.e('Error closing database: $e');
          // Continue with cleanup even if close fails
        } finally {
          // Always reset these values even if close fails
          _database = null;
          isInitialized = false;
          _currentAccountId = null; // Explicitly clear account ID when closing
        }
      }
    });
  }

  // Lock to prevent concurrent initialization
  static final _initLock = Lock();

  // Cache for frequently accessed data
  static List<Customer>? _cachedCustomers;
  static String? _cachedCustomersAccountId;
  static DateTime? _cachedCustomersTimestamp;
  static const Duration _cacheValidDuration = Duration(minutes: 5);

  // Initialize the database
  static Future<void> initialize({String? accountId}) async {
    // Use a lock to prevent concurrent initialization attempts
    return _initLock.synchronized(() async {
      // If already initialized with the same account, just return
      if (isInitialized && accountId == _currentAccountId) {
        debugPrint('DB_SERVICE_INIT: Already initialized for account: $accountId');
        return;
      }

      debugPrint('DB_SERVICE_INIT: Initializing database for account: ${accountId ?? 'default'}');
      if (_database != null && _database!.isOpen) {
        debugPrint('DB_SERVICE_INIT: Closing existing DB for account $_currentAccountId before re-initializing for $accountId...');
      }

      // Store the current account ID
      _currentAccountId = accountId;

      try {
        // Close existing database if open
        if (_database != null) {
          await _database!.close();
          _database = null;
          isInitialized = false;
        }

        // Clear any cached data that might be in memory
        _clearInMemoryCache();

        // Initialize the database
        _database = await _initDatabase();
        isInitialized = true;
        debugPrint('DB_SERVICE_INIT: DatabaseService initialized. DB is open: ${_database?.isOpen}');
        debugPrint('DB_SERVICE_INIT: Current account ID: $_currentAccountId');
      } catch (e) {
        // Reset initialization flag on error
        isInitialized = false;
        _database = null;
        _logger.e('Error initializing database: $e');
        throw Exception('Failed to initialize database: $e');
      }
    });
  }

  // Initialize the database
  static Future<Database> _initDatabase() async {
    try {
      // Get the documents directory
      final Directory documentsDirectory =
          await getApplicationDocumentsDirectory();

      // Create database name based on account ID
      final String dbName = _currentAccountId != null
          ? 'account_$_currentAccountId.db'
          : 'default.db';

      final String path = join(documentsDirectory.path, dbName);
      debugPrint('DB_SERVICE_INIT: Database path: $path for account: $_currentAccountId');

      // Check if the database file exists
      final dbFile = File(path);
      final fileExists = await dbFile.exists();
      debugPrint('DB_SERVICE_INIT: Database file exists: $fileExists');
      if (fileExists) {
        final fileSize = await dbFile.length();
        debugPrint('DB_SERVICE_INIT: Database file size: $fileSize bytes');
      }

      // Ensure the directory exists
      await Directory(documentsDirectory.path).create(recursive: true);

      // Open the database with more detailed error handling
      try {
        debugPrint('DB_SERVICE_INIT: Opening database for account: $_currentAccountId');
        final db = await openDatabase(
          path,
          version: 4, // Increased version to trigger migration for schema fix
          onCreate: _onCreate,
          onUpgrade: _onUpgrade,
          onOpen: (db) async {
            debugPrint('DB_SERVICE_INIT: Database opened successfully: ${db.path}');
            debugPrint('DB_SERVICE_INIT: DB is open: ${db.isOpen}');
            // Verify schema on every database open to ensure it's correct
            await _verifyDatabaseSchema(db);
          },
          // Add settings to ensure we have isolated connections
          singleInstance: false, // Make sure we get a new instance each time
        );

        return db;
      } catch (dbError) {
        _logger.e('Error opening database: $dbError');

        // Try to recover by deleting and recreating the database if it's corrupted
        if (dbError.toString().contains('database disk image is malformed') ||
            dbError.toString().contains('database is corrupted')) {
          _logger.w('Attempting to recover from corrupted database');

          // Delete the corrupted database file
          final dbFile = File(path);
          if (await dbFile.exists()) {
            await dbFile.delete();
            _logger.i('Deleted corrupted database file');
          }

          // Try to open again
          return await openDatabase(
            path,
            version: 4, // Increased version to match
            onCreate: _onCreate,
            onUpgrade: _onUpgrade,
            singleInstance: false,
          );
        }

        // If not a corruption error, rethrow
        rethrow;
      }
    } catch (e) {
      _logger.e('Error initializing database: $e');
      throw Exception('Failed to initialize database: $e');
    }
  }

  // Create the database tables
  static Future<void> _onCreate(Database db, int version) async {
    try {
      // Create Customer table
      await db.execute('''
        CREATE TABLE customers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          contactNumber TEXT,
          createdAt TEXT NOT NULL,
          balance REAL NOT NULL DEFAULT 0.0,
          accountId TEXT
        )
      ''');

      // Create Bill table
      await db.execute('''
        CREATE TABLE bills (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          customerId INTEGER NOT NULL,
          billDate TEXT NOT NULL,
          startTime TEXT NOT NULL,
          endTime TEXT NOT NULL,
          durationHours REAL NOT NULL,
          durationHoursWhole INTEGER NOT NULL,
          durationMinutes INTEGER NOT NULL,
          hourlyRate REAL NOT NULL,
          amount REAL NOT NULL,
          discountAmount REAL,
          discountTime REAL,
          remarks TEXT,
          isPaid INTEGER NOT NULL DEFAULT 0,
          isPartiallyPaid INTEGER NOT NULL DEFAULT 0,
          partialAmount REAL,
          paidDate TEXT,
          paidAmount REAL NOT NULL DEFAULT 0,
          accountId TEXT,
          FOREIGN KEY (customerId) REFERENCES customers (id) ON DELETE CASCADE
        )
      ''');

      // Create Payment table
      await db.execute('''
        CREATE TABLE payments (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          customerId INTEGER NOT NULL,
          billId INTEGER NOT NULL DEFAULT 0,
          paymentDate TEXT NOT NULL,
          amount REAL NOT NULL,
          paymentMethod TEXT,
          remarks TEXT,
          accountId TEXT,
          FOREIGN KEY (customerId) REFERENCES customers (id) ON DELETE CASCADE
        )
      ''');

      // Create CustomerCredit table
      await db.execute('''
        CREATE TABLE customer_credits (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          customerId INTEGER NOT NULL UNIQUE,
          amount REAL NOT NULL,
          lastUpdated TEXT NOT NULL,
          notes TEXT NOT NULL DEFAULT '',
          accountId TEXT,
          FOREIGN KEY (customerId) REFERENCES customers (id) ON DELETE CASCADE
        )
      ''');

      // Create PaymentAllocation table
      await db.execute('''
        CREATE TABLE payment_allocations (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          paymentId INTEGER NOT NULL,
          billId INTEGER NOT NULL,
          amount REAL NOT NULL,
          remarks TEXT,
          createdAt TEXT NOT NULL,
          accountId TEXT,
          FOREIGN KEY (paymentId) REFERENCES payments (id) ON DELETE CASCADE,
          FOREIGN KEY (billId) REFERENCES bills (id) ON DELETE CASCADE
        )
      ''');

      // Create Expenses table
      await db.execute('''
        CREATE TABLE expenses (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          description TEXT NOT NULL,
          amount REAL NOT NULL,
          date TEXT NOT NULL,
          category TEXT NOT NULL,
          paymentMethod TEXT,
          remarks TEXT,
          accountId TEXT
        )
      ''');

      // Create Settings table
      await db.execute('''
        CREATE TABLE settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          accountId TEXT,
          key TEXT NOT NULL,
          value TEXT NOT NULL,
          UNIQUE(key, accountId)
        )
      ''');

      // Create indexes
      await db.execute('CREATE INDEX idx_customers_name ON customers (name)');
      await db
          .execute('CREATE INDEX idx_bills_customerId ON bills (customerId)');
      await db.execute('CREATE INDEX idx_bills_billDate ON bills (billDate)');
      await db.execute('CREATE INDEX idx_bills_isPaid ON bills (isPaid)');
      await db.execute(
          'CREATE INDEX idx_bills_isPartiallyPaid ON bills (isPartiallyPaid)');
      await db.execute(
          'CREATE INDEX idx_payments_customerId ON payments (customerId)');
      await db.execute('CREATE INDEX idx_payments_billId ON payments (billId)');
      await db.execute(
          'CREATE INDEX idx_payments_paymentDate ON payments (paymentDate)');
      await db.execute(
          'CREATE INDEX idx_customer_credits_customerId ON customer_credits (customerId)');
      await db.execute(
          'CREATE INDEX idx_payment_allocations_createdAt ON payment_allocations (createdAt)');
      await db.execute(
          'CREATE INDEX idx_settings_key_accountId ON settings (key, accountId)');
      await db.execute('CREATE INDEX idx_expenses_date ON expenses (date)');
      await db
          .execute('CREATE INDEX idx_expenses_category ON expenses (category)');

      _logger.i('Database tables created successfully');
    } catch (e) {
      _logger.e('Error creating database tables: $e');
      throw Exception('Failed to create database tables: $e');
    }
  }

  // Clear any in-memory cache
  static void _clearInMemoryCache() {
    // Clear database cache
    _cachedCustomers = null;
    _cachedCustomersAccountId = null;
    _cachedCustomersTimestamp = null;
    _logger.i('Clearing in-memory cache and database cache');
  }

  // Upgrade the database
  static Future<void> _onUpgrade(
      Database db, int oldVersion, int newVersion) async {
    _logger.i('Upgrading database from version $oldVersion to $newVersion');

    try {
      // Handle database upgrades based on version
      if (oldVersion < 2) {
        _logger.i('Upgrading from version 1 to 2');
        await _addBalanceColumnIfNeeded(db);
      }

      if (oldVersion < 3) {
        _logger.i('Upgrading from version 2 to 3');
        // Version 3 upgrades would go here
        // This is a placeholder for future upgrades
      }

      if (oldVersion < 4) {
        _logger.i('Upgrading from version 3 to 4');
        // Ensure the balance column exists and is properly set up
        await _addBalanceColumnIfNeeded(db);

        // Verify all tables have the correct schema
        await _verifyDatabaseSchema(db);
      }

      _logger.i('Database upgrade completed successfully');
    } catch (e) {
      _logger.e('Error upgrading database: $e');

      // Try to recover by verifying the schema
      try {
        await _verifyDatabaseSchema(db);
      } catch (verifyError) {
        _logger.e('Error verifying schema during recovery: $verifyError');
      }

      // Rethrow the original error
      throw Exception('Failed to upgrade database: $e');
    }
  }

  // Helper method to add balance column if needed
  static Future<void> _addBalanceColumnIfNeeded(Database db) async {
    try {
      // Check if balance column exists
      final List<Map<String, dynamic>> tableInfo =
          await db.rawQuery("PRAGMA table_info(customers)");

      final bool hasBalanceColumn =
          tableInfo.any((column) => column['name'] == 'balance');

      // Add balance column to customers table if it doesn't exist
      if (!hasBalanceColumn) {
        _logger.i('Adding balance column to customers table');
        await db.execute(
            'ALTER TABLE customers ADD COLUMN balance REAL NOT NULL DEFAULT 0.0');
        _logger.i('Balance column added successfully');
      }

      // Update balance values for existing customers based on their credits and outstanding bills
      _logger.i('Updating balance values for existing customers');

      // Get all customers
      final List<Map<String, dynamic>> customers = await db.query('customers');

      // For each customer, calculate and update their balance
      for (final customer in customers) {
        final int customerId = customer['id'];

        // Get customer credit
        final List<Map<String, dynamic>> credits = await db.query(
          'customer_credits',
          where: 'customerId = ?',
          whereArgs: [customerId],
        );

        double creditAmount = 0.0;
        if (credits.isNotEmpty) {
          creditAmount = credits.first['amount'] ?? 0.0;
        }

        // Get outstanding bills
        final List<Map<String, dynamic>> bills = await db.query(
          'bills',
          where: 'customerId = ? AND isPaid = 0',
          whereArgs: [customerId],
        );

        double outstandingAmount = 0.0;
        for (final bill in bills) {
          final double billAmount = bill['amount'] ?? 0.0;
          final double partialAmount = bill['partialAmount'] ?? 0.0;
          outstandingAmount += (billAmount - partialAmount);
        }

        // Calculate net balance (credit - outstanding)
        final double balance = creditAmount - outstandingAmount;

        // Update customer balance
        await db.update(
          'customers',
          {'balance': balance},
          where: 'id = ?',
          whereArgs: [customerId],
        );
      }

      _logger.i('Customer balances updated successfully');
    } catch (e) {
      _logger.e('Error adding balance column: $e');
      rethrow; // Rethrow to be handled by the caller
    }
  }

  // Get the current account ID
  static String? getCurrentAccountId() {
    return _currentAccountId;
  }

  // Customer operations
  static Future<List<Customer>> getAllCustomers() async {
    debugPrint("DB_SERVICE_QUERY: getAllCustomers() called");

    if (!isInitialized) {
      debugPrint("DB_SERVICE_QUERY: Database not initialized, initializing now...");
      await initialize();
    }

    final activeAccountIdFromAccountService = AccountService.currentAccount?.id;
    debugPrint("DB_SERVICE_QUERY: AccountService.currentAccount.id = $activeAccountIdFromAccountService");
    debugPrint("DB_SERVICE_QUERY: DatabaseService._currentAccountId = $_currentAccountId");

    // Fix account mismatch by re-initializing database if needed
    if (activeAccountIdFromAccountService != _currentAccountId) {
      debugPrint("DB_SERVICE_QUERY: MISMATCH! Re-initializing database for correct account...");
      await initialize(accountId: activeAccountIdFromAccountService);
    }

    // Check cache first
    if (_cachedCustomers != null &&
        _cachedCustomersAccountId == _currentAccountId &&
        _cachedCustomersTimestamp != null &&
        DateTime.now().difference(_cachedCustomersTimestamp!) < _cacheValidDuration) {
      debugPrint("DB_SERVICE_QUERY: Returning cached customers (${_cachedCustomers!.length} records)");
      return _cachedCustomers!;
    }

    // For now, avoid compute isolate due to BackgroundIsolateBinaryMessenger issue
    // Use regular async database query but optimize it
    final db = await database;
    debugPrint("DB_SERVICE_QUERY: Database instance obtained, isOpen: ${db.isOpen}");
    debugPrint("DB_SERVICE_QUERY: Querying customers for accountId: $_currentAccountId");

    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: 'accountId = ? OR accountId IS NULL',
      whereArgs: [_currentAccountId],
    );

    debugPrint("DB_SERVICE_QUERY: Found ${maps.length} customer records for account $_currentAccountId");
    if (maps.isNotEmpty) {
      debugPrint("DB_SERVICE_QUERY: First customer accountId: ${maps.first['accountId']}");
      debugPrint("DB_SERVICE_QUERY: First customer name: ${maps.first['name']}");
    }

    final customers = List.generate(maps.length, (i) {
      return Customer.fromMap(maps[i]);
    });

    // Cache the results
    _cachedCustomers = customers;
    _cachedCustomersAccountId = _currentAccountId;
    _cachedCustomersTimestamp = DateTime.now();
    debugPrint("DB_SERVICE_QUERY: Cached ${customers.length} customers for account $_currentAccountId");

    return customers;
  }

  // Helper method to get database path
  static Future<String> _getDatabasePath(String? accountId) async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final dbName = accountId != null ? 'account_$accountId.db' : 'tubewell_billing.db';
    return join(documentsDirectory.path, dbName);
  }

  // Background isolate function for getAllCustomers
  static Future<List<Customer>> _getAllCustomersInBackground(String? accountId) async {
    // Re-initialize database connection in isolate
    final db = await openDatabase(
      await _getDatabasePath(accountId),
      version: 4,
      readOnly: true, // Read-only for better performance
    );

    try {
      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        where: 'accountId = ? OR accountId IS NULL',
        whereArgs: [accountId],
      );

      debugPrint("DB_SERVICE_QUERY: Found ${maps.length} customer records for account $accountId");
      if (maps.isNotEmpty) {
        debugPrint("DB_SERVICE_QUERY: First customer accountId: ${maps.first['accountId']}");
        debugPrint("DB_SERVICE_QUERY: First customer name: ${maps.first['name']}");
      }

      return List.generate(maps.length, (i) {
        return Customer.fromMap(maps[i]);
      });
    } finally {
      await db.close();
    }
  }

  /// Get customers with pagination and search support
  static Future<List<Customer>> getCustomersPaginated({
    int page = 0,
    int pageSize = 20,
    String? searchQuery,
    String? sortOption,
  }) async {
    if (!isInitialized) await initialize();

    final db = await database;
    final offset = page * pageSize;

    // Build query conditions
    String whereClause = 'accountId = ? OR accountId IS NULL';
    List<dynamic> whereArgs = [_currentAccountId];

    // Add search condition if provided
    if (searchQuery != null && searchQuery.isNotEmpty) {
      whereClause += ' AND (name LIKE ? OR contactNumber LIKE ?)';
      whereArgs.add('%$searchQuery%');
      whereArgs.add('%$searchQuery%');
    }

    // Determine sort order
    String orderBy = 'name ASC'; // Default sort
    if (sortOption != null) {
      switch (sortOption) {
        case 'nameAsc':
          orderBy = 'name ASC';
          break;
        case 'nameDesc':
          orderBy = 'name DESC';
          break;
        // Other sort options would be handled in memory after fetching data
      }
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: orderBy,
      limit: pageSize,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return Customer.fromMap(maps[i]);
    });
  }

  static Future<Customer?> getCustomerById(int id) async {
    if (!isInitialized) await initialize();

    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: 'id = ? AND (accountId = ? OR accountId IS NULL)',
      whereArgs: [id, _currentAccountId],
    );

    if (maps.isEmpty) {
      return null;
    }

    return Customer.fromMap(maps.first);
  }

  static Future<int> saveCustomer(Customer customer) async {
    if (!isInitialized) await initialize();

    final db = await database;

    // Add the current account ID to the customer data
    final customerMap = customer.toMap();
    customerMap['accountId'] = _currentAccountId;

    // Ensure balance is included
    if (!customerMap.containsKey('balance')) {
      customerMap['balance'] = 0.0;
    }

    try {
      return await db.transaction((txn) async {
        return await txn.insert(
          'customers',
          customerMap,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      });
    } catch (e) {
      // Special handling for "no column named balance" error
      if (e.toString().contains('no column named balance')) {
        _logger.w('Missing balance column detected, attempting to fix schema');

        try {
          // Verify table schema and try to fix it
          await _verifyDatabaseSchema();

          // Wait a moment to ensure the schema changes are committed
          await Future.delayed(const Duration(milliseconds: 500));

          // Retry the insert after fixing the schema
          try {
            return await db.transaction((txn) async {
              return await txn.insert(
                'customers',
                customerMap,
                conflictAlgorithm: ConflictAlgorithm.replace,
              );
            });
          } catch (retryError) {
            _logger.e('Error on retry after schema fix: $retryError');

            // If we still have issues, try a more direct approach
            // Create a simplified customer map with only essential fields
            final Map<String, dynamic> simplifiedMap = {
              'name': customer.name,
              'contactNumber': customer.contactNumber,
              'createdAt': customer.createdAt.toIso8601String(),
              'balance': 0.0,
              'accountId': _currentAccountId,
            };

            // Try one more time with the simplified map
            return await db.insert(
              'customers',
              simplifiedMap,
              conflictAlgorithm: ConflictAlgorithm.replace,
            );
          }
        } catch (fixError) {
          _logger.e('Failed to fix schema: $fixError');

          // Last resort - try to recreate the table and then insert
          try {
            await _recreateCustomersTable(db);

            // Try one final time after recreating the table
            return await db.insert(
              'customers',
              customerMap,
              conflictAlgorithm: ConflictAlgorithm.replace,
            );
          } catch (lastError) {
            _logger.e('All recovery attempts failed: $lastError');
            throw Exception(
                'Failed to save customer after multiple recovery attempts');
          }
        }
      } else {
        // Not a schema issue, rethrow the original error
        rethrow;
      }
    }
  }

  static Future<bool> deleteCustomer(int id) async {
    if (!isInitialized) await initialize();

    final db = await database;

    return await db.transaction((txn) async {
      try {
        // First verify the customer exists and belongs to the current account
        final List<Map<String, dynamic>> customerMaps = await txn.query(
          'customers',
          where: 'id = ? AND (accountId = ? OR accountId IS NULL)',
          whereArgs: [id, _currentAccountId],
        );

        if (customerMaps.isEmpty) {
          return false;
        }

        // Delete the customer - SQLite will handle cascading deletes
        // because we set up foreign key constraints in the database schema
        final result = await txn.delete(
          'customers',
          where: 'id = ? AND (accountId = ? OR accountId IS NULL)',
          whereArgs: [id, _currentAccountId],
        );

        return result > 0;
      } catch (e) {
        _logger.e('Error deleting customer: $e');
        return false;
      }
    });
  }

  static Future<int> updateCustomer(Customer customer) async {
    if (!isInitialized) await initialize();

    final db = await database;

    // Add the current account ID to the customer data
    final customerMap = customer.toMap();
    customerMap['accountId'] = _currentAccountId;

    // Ensure balance is included
    if (!customerMap.containsKey('balance')) {
      customerMap['balance'] = 0.0;
    }

    try {
      return await db.transaction((txn) async {
        return await txn.update(
          'customers',
          customerMap,
          where: 'id = ? AND (accountId = ? OR accountId IS NULL)',
          whereArgs: [customer.id, _currentAccountId],
        );
      });
    } catch (e) {
      // Special handling for "no column named balance" error
      if (e.toString().contains('no column named balance')) {
        _logger.w(
            'Missing balance column detected during update, attempting to fix schema');

        try {
          // Verify table schema and try to fix it
          await _verifyDatabaseSchema();

          // Wait a moment to ensure the schema changes are committed
          await Future.delayed(const Duration(milliseconds: 500));

          // Retry the update after fixing the schema
          return await db.transaction((txn) async {
            return await txn.update(
              'customers',
              customerMap,
              where: 'id = ? AND (accountId = ? OR accountId IS NULL)',
              whereArgs: [customer.id, _currentAccountId],
            );
          });
        } catch (fixError) {
          _logger.e('Failed to fix schema during update: $fixError');

          // Last resort - try to recreate the table and then update
          try {
            await _recreateCustomersTable(db);

            // Try one final time after recreating the table
            return await db.update(
              'customers',
              customerMap,
              where: 'id = ? AND (accountId = ? OR accountId IS NULL)',
              whereArgs: [customer.id, _currentAccountId],
            );
          } catch (lastError) {
            _logger.e('All recovery attempts failed during update: $lastError');
            throw Exception(
                'Failed to update customer after multiple recovery attempts');
          }
        }
      } else {
        // Not a schema issue, rethrow the original error
        rethrow;
      }
    }
  }

  // Bill operations
  static Future<List<Bill>> getAllBills() async {
    if (!isInitialized) await initialize();

    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'bills',
      where: 'accountId = ? OR accountId IS NULL',
      whereArgs: [_currentAccountId],
      orderBy: 'billDate DESC',
    );

    return List.generate(maps.length, (i) {
      return Bill.fromMap(maps[i]);
    });
  }

  static Future<Bill?> getBillById(int id) async {
    if (!isInitialized) await initialize();

    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'bills',
      where: 'id = ? AND (accountId = ? OR accountId IS NULL)',
      whereArgs: [id, _currentAccountId],
    );

    if (maps.isEmpty) {
      return null;
    }

    return Bill.fromMap(maps.first);
  }

  static Future<int> saveBill(Bill bill) async {
    if (!isInitialized) await initialize();

    final db = await database;

    // Add the current account ID to the bill data
    final billMap = bill.toMap();
    billMap['accountId'] = _currentAccountId;

    return await db.transaction((txn) async {
      return await txn.insert(
        'bills',
        billMap,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    });
  }

  static Future<bool> deleteBill(int id) async {
    if (!isInitialized) await initialize();

    final db = await database;

    return await db.transaction((txn) async {
      final result = await txn.delete(
        'bills',
        where: 'id = ? AND (accountId = ? OR accountId IS NULL)',
        whereArgs: [id, _currentAccountId],
      );

      return result > 0;
    });
  }

  // Get bills by customer ID
  static Future<List<Bill>> getBillsByCustomer(int customerId) async {
    if (!isInitialized) await initialize();

    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'bills',
      where: 'customerId = ? AND (accountId = ? OR accountId IS NULL)',
      whereArgs: [customerId, _currentAccountId],
      orderBy: 'billDate DESC',
    );

    return List.generate(maps.length, (i) {
      return Bill.fromMap(maps[i]);
    });
  }

  // Update bill
  static Future<int> updateBill(Bill bill) async {
    if (!isInitialized) await initialize();

    final db = await database;

    // Add the current account ID to the bill data
    final billMap = bill.toMap();
    billMap['accountId'] = _currentAccountId;

    return await db.transaction((txn) async {
      return await txn.update(
        'bills',
        billMap,
        where: 'id = ? AND (accountId = ? OR accountId IS NULL)',
        whereArgs: [bill.id, _currentAccountId],
      );
    });
  }

  // Get customers by IDs
  Future<List<Customer>> getCustomersByIds(List<int> ids) async {
    if (!isInitialized) await initialize();

    if (ids.isEmpty) {
      return [];
    }

    final db = await database;

    // Create placeholders for the IN clause
    final placeholders = List.generate(ids.length, (index) => '?').join(',');

    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: 'id IN ($placeholders) AND (accountId = ? OR accountId IS NULL)',
      whereArgs: [...ids, _currentAccountId],
    );

    return List.generate(maps.length, (i) {
      return Customer.fromMap(maps[i]);
    });
  }

  /// Get all payments with pagination and search support
  static Future<List<Payment>> getAllPayments({
    int offset = 0,
    int limit = 20,
    String? searchQuery,
    String? paymentMethod,
    DateTime? startDate,
    DateTime? endDate,
    int? customerId,
  }) async {
    if (!isInitialized) await initialize();

    final db = await database;

    String whereClause = 'accountId = ? OR accountId IS NULL';
    List<dynamic> whereArgs = [_currentAccountId];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      final amount = double.tryParse(searchQuery);
      if (amount != null) {
        whereClause += ' AND amount = ?';
        whereArgs.add(amount);
      }
    }

    if (paymentMethod != null && paymentMethod.isNotEmpty) {
      whereClause += ' AND paymentMethod LIKE ?';
      whereArgs.add('%$paymentMethod%');
    }

    // Filter by customer ID
    if (customerId != null) {
      whereClause += ' AND customerId = ?';
      whereArgs.add(customerId);
    }

    // Filter by date range
    if (startDate != null) {
      whereClause += ' AND paymentDate >= ?';
      whereArgs.add(startDate.millisecondsSinceEpoch);
    }

    if (endDate != null) {
      // Add one day to include the end date fully
      final endDatePlusDay = endDate.add(const Duration(days: 1));
      whereClause += ' AND paymentDate < ?';
      whereArgs.add(endDatePlusDay.millisecondsSinceEpoch);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      where: whereClause.isNotEmpty ? whereClause : null,
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: 'paymentDate DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return Payment.fromMap(maps[i]);
    });
  }

  /// Get payments for specific customer with pagination and search support
  static Future<List<Payment>> getPaymentsByCustomer(
    int customerId, {
    int offset = 0,
    int limit = 20,
    String? searchQuery,
    String? paymentMethod,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    if (!isInitialized) await initialize();

    final db = await database;

    String whereClause =
        'customerId = ? AND (accountId = ? OR accountId IS NULL)';
    List<dynamic> whereArgs = [customerId, _currentAccountId];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      final amount = double.tryParse(searchQuery);
      if (amount != null) {
        whereClause += ' AND amount = ?';
        whereArgs.add(amount);
      }
    }

    if (paymentMethod != null && paymentMethod.isNotEmpty) {
      whereClause += ' AND paymentMethod LIKE ?';
      whereArgs.add('%$paymentMethod%');
    }

    // Filter by date range
    if (startDate != null) {
      whereClause += ' AND paymentDate >= ?';
      whereArgs.add(startDate.millisecondsSinceEpoch);
    }

    if (endDate != null) {
      // Add one day to include the end date fully
      final endDatePlusDay = endDate.add(const Duration(days: 1));
      whereClause += ' AND paymentDate < ?';
      whereArgs.add(endDatePlusDay.millisecondsSinceEpoch);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'paymentDate DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return Payment.fromMap(maps[i]);
    });
  }

  // Payment operations
  static Future<Payment?> getPaymentById(int id) async {
    if (!isInitialized) await initialize();

    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) {
      return null;
    }

    return Payment.fromMap(maps.first);
  }

  static Future<int> savePayment(Payment payment) async {
    if (!isInitialized) await initialize();

    final db = await database;

    // Add the current account ID to the payment data
    final paymentMap = payment.toMap();
    paymentMap['accountId'] = _currentAccountId;

    return await db.transaction((txn) async {
      return await txn.insert(
        'payments',
        paymentMap,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    });
  }

  static Future<bool> deletePayment(int id) async {
    if (!isInitialized) await initialize();

    final db = await database;

    return await db.transaction((txn) async {
      final result = await txn.delete(
        'payments',
        where: 'id = ? AND (accountId = ? OR accountId IS NULL)',
        whereArgs: [id, _currentAccountId],
      );

      return result > 0;
    });
  }

  // Get payments by bill ID
  static Future<List<Payment>> getPaymentsByBill(int billId) async {
    if (!isInitialized) await initialize();

    final db = await database;

    // First get payments directly linked to this bill
    final List<Map<String, dynamic>> directPaymentMaps = await db.query(
      'payments',
      where: 'billId = ? AND (accountId = ? OR accountId IS NULL)',
      whereArgs: [billId, _currentAccountId],
      orderBy: 'paymentDate ASC',
    );

    final List<Payment> directPayments =
        List.generate(directPaymentMaps.length, (i) {
      return Payment.fromMap(directPaymentMaps[i]);
    });

    // Now get payments linked through payment allocations
    final List<Map<String, dynamic>> allocationMaps = await db.query(
      'payment_allocations',
      where: 'billId = ? AND (accountId = ? OR accountId IS NULL)',
      whereArgs: [billId, _currentAccountId],
    );

    final List<int> paymentIds =
        allocationMaps.map((map) => map['paymentId'] as int).toList();

    List<Payment> allocationPayments = [];

    if (paymentIds.isNotEmpty) {
      // Create placeholders for the IN clause
      final placeholders =
          List.generate(paymentIds.length, (index) => '?').join(',');

      final List<Map<String, dynamic>> paymentMaps = await db.query(
        'payments',
        where: 'id IN ($placeholders)',
        whereArgs: paymentIds,
      );

      allocationPayments = List.generate(paymentMaps.length, (i) {
        return Payment.fromMap(paymentMaps[i]);
      });
    }

    // Combine both sets and remove duplicates
    final allPayments = [...directPayments, ...allocationPayments];

    // Remove duplicates based on payment ID
    final uniquePayments = <int, Payment>{};
    for (var payment in allPayments) {
      uniquePayments[payment.id] = payment;
    }

    return uniquePayments.values.toList()
      ..sort((a, b) => a.paymentDate.compareTo(b.paymentDate));
  }

  // Save payment allocation
  static Future<int> savePaymentAllocation(PaymentAllocation allocation) async {
    if (!isInitialized) await initialize();

    final db = await database;

    // Add the current account ID to the allocation data
    final allocationMap = allocation.toMap();
    allocationMap['accountId'] = _currentAccountId;

    return await db.transaction((txn) async {
      return await txn.insert(
        'payment_allocations',
        allocationMap,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    });
  }

  // Get payment allocations by payment ID
  static Future<List<PaymentAllocation>> getPaymentAllocationsByPaymentId(
      int paymentId) async {
    if (!isInitialized) await initialize();

    final db = await database;

    // First check if the payment exists
    final List<Map<String, dynamic>> paymentMaps = await db.query(
      'payments',
      where: 'id = ?',
      whereArgs: [paymentId],
    );

    if (paymentMaps.isEmpty) {
      return [];
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'payment_allocations',
      where: 'paymentId = ? AND (accountId = ? OR accountId IS NULL)',
      whereArgs: [paymentId, _currentAccountId],
    );

    return List.generate(maps.length, (i) {
      return PaymentAllocation.fromMap(maps[i]);
    });
  }

  // Get payment allocations by bill ID
  static Future<List<PaymentAllocation>> getPaymentAllocationsByBillId(
      int billId) async {
    if (!isInitialized) await initialize();

    final db = await database;

    // First check if the bill exists
    final List<Map<String, dynamic>> billMaps = await db.query(
      'bills',
      where: 'id = ?',
      whereArgs: [billId],
    );

    if (billMaps.isEmpty) {
      return [];
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'payment_allocations',
      where: 'billId = ? AND (accountId = ? OR accountId IS NULL)',
      whereArgs: [billId, _currentAccountId],
    );

    return List.generate(maps.length, (i) {
      return PaymentAllocation.fromMap(maps[i]);
    });
  }

  // Customer Credit operations
  static Future<CustomerCredit?> getCustomerCredit(int customerId) async {
    if (!isInitialized) await initialize();

    final db = await database;

    final List<Map<String, dynamic>> maps = await db.query(
      'customer_credits',
      where: 'customerId = ? AND (accountId = ? OR accountId IS NULL)',
      whereArgs: [customerId, _currentAccountId],
      limit: 1,
    );

    if (maps.isEmpty) {
      return null;
    }

    return CustomerCredit.fromMap(maps.first);
  }

  /// Get all customer credits for the current account
  static Future<List<CustomerCredit>> getAllCustomerCredits() async {
    if (!isInitialized) await initialize();

    final db = await database;

    final List<Map<String, dynamic>> maps = await db.query(
      'customer_credits',
      where: 'accountId = ? OR accountId IS NULL',
      whereArgs: [_currentAccountId],
    );

    return List.generate(maps.length, (i) {
      return CustomerCredit.fromMap(maps[i]);
    });
  }

  static Future<int> saveCustomerCredit(CustomerCredit credit) async {
    if (!isInitialized) await initialize();

    final db = await database;

    // Add the current account ID to the credit data
    final creditMap = credit.toMap();
    creditMap['accountId'] = _currentAccountId;

    return await db.transaction((txn) async {
      return await txn.insert(
        'customer_credits',
        creditMap,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    });
  }

  static Future<bool> updateCustomerCreditAmount(int customerId, double amount,
      [String notes = '']) async {
    if (!isInitialized) await initialize();

    try {
      // Get existing credit or create new one
      CustomerCredit? credit = await getCustomerCredit(customerId);

      if (credit == null) {
        // Create new credit
        credit = CustomerCredit.create(
          customerId: customerId,
          amount: amount,
          notes: notes,
        );
      } else {
        // Update existing credit
        credit.amount = amount;
        credit.lastUpdated = DateTime.now();
        if (notes.isNotEmpty) {
          credit.notes = notes;
        }
      }

      // Save the credit
      final creditId = await saveCustomerCredit(credit);

      // Verify the credit was saved
      return creditId > 0;
    } catch (e) {
      _logger.e('Error updating customer credit: $e');
      return false;
    }
  }

  /// Debug helper to print customer credit info
  static Future<void> debugCustomerCredit(int customerId) async {
    try {
      // Implementation would go here if needed
      // This is a debug helper method, so we can leave it empty for now
    } catch (e) {
      // Silently ignore errors in debug methods
    }
  }

  // For backward compatibility
  static Future<bool> safeDeleteCustomer(int id, {int maxRetries = 3}) async {
    return deleteCustomer(id);
  }

  static Future<List<Bill>> getBillsByCustomerPaginated(int customerId,
      {int page = 0, int pageSize = 20}) async {
    if (!isInitialized) await initialize();

    final db = await database;
    final offset = page * pageSize;

    final List<Map<String, dynamic>> maps = await db.query(
      'bills',
      where: 'customerId = ? AND (accountId = ? OR accountId IS NULL)',
      whereArgs: [customerId, _currentAccountId],
      orderBy: 'billDate DESC',
      limit: pageSize,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return Bill.fromMap(maps[i]);
    });
  }

  static Future<List<Payment>> getPaymentsByCustomerPaginated(int customerId,
      {int page = 0, int pageSize = 20}) async {
    if (!isInitialized) await initialize();

    final db = await database;
    final offset = page * pageSize;

    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      where: 'customerId = ? AND (accountId = ? OR accountId IS NULL)',
      whereArgs: [customerId, _currentAccountId],
      orderBy: 'paymentDate DESC',
      limit: pageSize,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return Payment.fromMap(maps[i]);
    });
  }

  static Future<Map<String, num>> getCustomerFinancialSummary(
      int customerId) async {
    if (!isInitialized) await initialize();

    final db = await database;

    // Get total billed amount
    final List<Map<String, dynamic>> billMaps = await db.query(
      'bills',
      where: 'customerId = ? AND (accountId = ? OR accountId IS NULL)',
      whereArgs: [customerId, _currentAccountId],
    );

    final bills = List.generate(billMaps.length, (i) {
      return Bill.fromMap(billMaps[i]);
    });

    double totalBilled = bills.fold(0, (sum, bill) => sum + bill.amount);
    int billCount = bills.length;

    // Get total paid amount
    final List<Map<String, dynamic>> paymentMaps = await db.query(
      'payments',
      where: 'customerId = ? AND (accountId = ? OR accountId IS NULL)',
      whereArgs: [customerId, _currentAccountId],
    );

    final payments = List.generate(paymentMaps.length, (i) {
      return Payment.fromMap(paymentMaps[i]);
    });

    double totalPaid = payments.fold(0, (sum, payment) => sum + payment.amount);

    // Get credit balance
    final creditPayments = payments.where((payment) => payment.billId == 0);
    double creditBalance =
        creditPayments.fold(0, (sum, payment) => sum + payment.amount);

    return {
      'totalBilled': totalBilled,
      'totalPaid': totalPaid,
      'billCount': billCount,
      'netBalance': totalPaid - totalBilled,
      'creditBalance': creditBalance
    };
  }

  static Future<List<Bill>> getBillsFiltered({
    DateTime? startDate,
    DateTime? endDate,
    bool? isPaid,
    int? customerId,
    String? searchQuery,
    int offset = 0,
    int limit = 20,
    bool sortDescending = true,
  }) async {
    if (!isInitialized) await initialize();

    final db = await database;

    // Build the WHERE clause
    List<String> whereConditions = [];
    List<dynamic> whereArgs = [];

    // Always filter by account ID (handle null case)
    if (_currentAccountId != null) {
      whereConditions.add('(accountId = ? OR accountId IS NULL)');
      whereArgs.add(_currentAccountId);
    } else {
      whereConditions.add('accountId IS NULL');
    }

    if (customerId != null) {
      whereConditions.add('customerId = ?');
      whereArgs.add(customerId);
    }

    if (isPaid != null) {
      // Handle partially paid bills correctly
      if (isPaid) {
        whereConditions.add('isPaid = 1');
      } else {
        // If looking for unpaid bills, include both unpaid and partially paid
        whereConditions.add('(isPaid = 0)');
      }
    }

    if (startDate != null) {
      whereConditions.add('billDate >= ?');
      whereArgs.add(startDate.toIso8601String());
    }

    if (endDate != null) {
      final endDatePlusOne = endDate.add(const Duration(days: 1));
      whereConditions.add('billDate < ?');
      whereArgs.add(endDatePlusOne.toIso8601String());
    }

    String whereClause = whereConditions.join(' AND ');

    // Get the bills
    final List<Map<String, dynamic>> maps = await db.query(
      'bills',
      where: whereClause.isEmpty ? null : whereClause,
      whereArgs: whereArgs.isEmpty ? null : whereArgs,
      orderBy: sortDescending ? 'billDate DESC' : 'billDate ASC',
      limit: limit,
      offset: offset,
    );

    List<Bill> bills = List.generate(maps.length, (i) {
      return Bill.fromMap(maps[i]);
    });

    // Apply search query filter if needed
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final lowercaseQuery = searchQuery.toLowerCase();

      // Get customer names for searching
      final customerIds = bills.map((bill) => bill.customerId).toSet().toList();

      if (customerIds.isNotEmpty) {
        // Create placeholders for the IN clause
        final placeholders =
            List.generate(customerIds.length, (index) => '?').join(',');

        final List<Map<String, dynamic>> customerMaps = await db.query(
          'customers',
          where: 'id IN ($placeholders)',
          whereArgs: customerIds,
        );

        final customers = List.generate(customerMaps.length, (i) {
          return Customer.fromMap(customerMaps[i]);
        });

        final customersMap = {for (var c in customers) c.id: c};

        // Filter bills by search query
        bills = bills.where((bill) {
          final customer = customersMap[bill.customerId];
          final customerName = customer?.name.toLowerCase() ?? '';
          final billId = bill.id.toString();
          final amount = bill.amount.toString();

          return customerName.contains(lowercaseQuery) ||
              billId.contains(lowercaseQuery) ||
              amount.contains(lowercaseQuery);
        }).toList();
      }
    }

    return bills;
  }

  static Future<int> countBillsFiltered({
    DateTime? startDate,
    DateTime? endDate,
    bool? isPaid,
    int? customerId,
    String? searchQuery,
  }) async {
    if (!isInitialized) await initialize();

    final db = await database;

    // Build the WHERE clause
    List<String> whereConditions = [];
    List<dynamic> whereArgs = [];

    // Always filter by account ID
    whereConditions.add('(accountId = ? OR accountId IS NULL)');
    whereArgs.add(_currentAccountId);

    if (customerId != null) {
      whereConditions.add('customerId = ?');
      whereArgs.add(customerId);
    }

    if (isPaid != null) {
      whereConditions.add('isPaid = ?');
      whereArgs.add(isPaid ? 1 : 0);
    }

    if (startDate != null) {
      whereConditions.add('billDate >= ?');
      whereArgs.add(startDate.toIso8601String());
    }

    if (endDate != null) {
      final endDatePlusOne = endDate.add(const Duration(days: 1));
      whereConditions.add('billDate < ?');
      whereArgs.add(endDatePlusOne.toIso8601String());
    }

    String whereClause = whereConditions.join(' AND ');

    // Count the bills
    final List<Map<String, dynamic>> result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM bills ${whereClause.isEmpty ? '' : 'WHERE $whereClause'}',
      whereArgs.isEmpty ? null : whereArgs,
    );

    int count = Sqflite.firstIntValue(result) ?? 0;

    // If there's a search query, we need to filter in memory
    if (searchQuery != null && searchQuery.isNotEmpty && count > 0) {
      // Get all the bills
      final List<Map<String, dynamic>> maps = await db.query(
        'bills',
        where: whereClause.isEmpty ? null : whereClause,
        whereArgs: whereArgs.isEmpty ? null : whereArgs,
      );

      List<Bill> bills = List.generate(maps.length, (i) {
        return Bill.fromMap(maps[i]);
      });

      final lowercaseQuery = searchQuery.toLowerCase();

      // Get customer names for searching
      final customerIds = bills.map((bill) => bill.customerId).toSet().toList();

      if (customerIds.isNotEmpty) {
        // Create placeholders for the IN clause
        final placeholders =
            List.generate(customerIds.length, (index) => '?').join(',');

        final List<Map<String, dynamic>> customerMaps = await db.query(
          'customers',
          where: 'id IN ($placeholders)',
          whereArgs: customerIds,
        );

        final customers = List.generate(customerMaps.length, (i) {
          return Customer.fromMap(customerMaps[i]);
        });

        final customersMap = {for (var c in customers) c.id: c};

        // Filter bills by search query
        bills = bills.where((bill) {
          final customer = customersMap[bill.customerId];
          final customerName = customer?.name.toLowerCase() ?? '';
          final billId = bill.id.toString();
          final amount = bill.amount.toString();

          return customerName.contains(lowercaseQuery) ||
              billId.contains(lowercaseQuery) ||
              amount.contains(lowercaseQuery);
        }).toList();

        count = bills.length;
      }
    }

    return count;
  }

  /// An optimized method to get bill summary with accurate paid/unpaid amounts
  /// This eliminates the inefficiency in the SummaryScreen's _getBillSummary method
  /// Now includes manual verification to ensure data consistency
  static Future<Map<String, num>> getBillsSummaryEfficient({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // Default summary structure
      final summary = {
        'totalCount': 0,
        'totalAmount': 0.0,
        'paidAmount': 0.0,
        'unpaidAmount': 0.0,
        'paidCount': 0,
        'unpaidCount': 0,
      };

      // Get the database
      final db = await database;

      // Build the query conditions for date filtering
      final List<String> whereConditions = [];
      final List<dynamic> whereArgs = [];

      // Always filter by account ID
      whereConditions.add('(accountId = ? OR accountId IS NULL)');
      whereArgs.add(_currentAccountId);

      if (startDate != null) {
        whereConditions.add('billDate >= ?');
        whereArgs.add(startDate.toIso8601String());
      }

      if (endDate != null) {
        // Add one day to include the end date fully
        final endDatePlusOne = endDate.add(const Duration(days: 1));
        whereConditions.add('billDate < ?');
        whereArgs.add(endDatePlusOne.toIso8601String());
      }

      final whereClause = whereConditions.isEmpty
          ? ''
          : 'WHERE ${whereConditions.join(' AND ')}';

      // Use a single efficient query to get all the summary data we need
      // This query calculates everything in the database rather than fetching all records
      final results = await db.rawQuery('''
        SELECT
          COUNT(*) as totalCount,
          SUM(amount) as totalAmount,
          SUM(CASE
              WHEN isPaid = 1 THEN amount
              WHEN isPartiallyPaid = 1 THEN IFNULL(partialAmount, 0)
              ELSE 0
          END) as paidAmount,
          SUM(CASE
              WHEN isPaid = 0 THEN
                CASE
                  WHEN isPartiallyPaid = 1 THEN amount - IFNULL(partialAmount, 0)
                  ELSE amount
                END
              ELSE 0
          END) as unpaidAmount,
          SUM(CASE WHEN isPaid = 1 THEN 1 ELSE 0 END) as paidCount,
          SUM(CASE WHEN isPaid = 0 THEN 1 ELSE 0 END) as unpaidCount
        FROM bills
        $whereClause
      ''', whereArgs);

      if (results.isNotEmpty) {
        final row = results.first;

        // Update the summary with values from the query
        // Use safer conversion methods to handle different numeric types from SQLite
        summary['totalCount'] = row['totalCount'] != null ? int.parse(row['totalCount'].toString()) : 0;
        summary['totalAmount'] = row['totalAmount'] != null ? double.parse(row['totalAmount'].toString()) : 0.0;
        summary['paidAmount'] = row['paidAmount'] != null ? double.parse(row['paidAmount'].toString()) : 0.0;
        summary['unpaidAmount'] = row['unpaidAmount'] != null ? double.parse(row['unpaidAmount'].toString()) : 0.0;
        summary['paidCount'] = row['paidCount'] != null ? int.parse(row['paidCount'].toString()) : 0;
        summary['unpaidCount'] = row['unpaidCount'] != null ? int.parse(row['unpaidCount'].toString()) : 0;
      }

      // Manual verification and correction (similar to transactions_screen.dart)
      // This ensures data consistency by double-checking the calculations
      final bills = await getBillsFiltered(
        startDate: startDate,
        endDate: endDate,
        limit: 10000, // Get all bills for verification
      );

      double manualPaidAmount = 0.0;
      double manualUnpaidAmount = 0.0;
      int manualPaidCount = 0;
      int manualUnpaidCount = 0;

      for (var bill in bills) {
        if (bill.isPaid) {
          manualPaidAmount += bill.amount;
          manualPaidCount++;
        } else if (bill.isPartiallyPaid && bill.partialAmount != null) {
          manualPaidAmount += bill.partialAmount!;
          manualUnpaidAmount += (bill.amount - bill.partialAmount!);
          manualUnpaidCount++;
        } else {
          manualUnpaidAmount += bill.amount;
          manualUnpaidCount++;
        }
      }

      // Verify and correct summary values if there are discrepancies
      if (summary['paidAmount'] != manualPaidAmount ||
          summary['unpaidAmount'] != manualUnpaidAmount ||
          summary['paidCount'] != manualPaidCount ||
          summary['unpaidCount'] != manualUnpaidCount) {

        // Log the discrepancy for debugging
        debugPrint('Bills summary discrepancy detected and corrected:');
        debugPrint('SQL paidAmount: ${summary['paidAmount']}, Manual: $manualPaidAmount');
        debugPrint('SQL unpaidAmount: ${summary['unpaidAmount']}, Manual: $manualUnpaidAmount');
        debugPrint('SQL paidCount: ${summary['paidCount']}, Manual: $manualPaidCount');
        debugPrint('SQL unpaidCount: ${summary['unpaidCount']}, Manual: $manualUnpaidCount');

        // Fix the summary values with manual calculations
        summary['paidAmount'] = manualPaidAmount;
        summary['unpaidAmount'] = manualUnpaidAmount;
        summary['paidCount'] = manualPaidCount;
        summary['unpaidCount'] = manualUnpaidCount;
        summary['totalCount'] = bills.length;
        summary['totalAmount'] = manualPaidAmount + manualUnpaidAmount;
      }

      return summary;
    } catch (e) {
      debugPrint('DatabaseService: Error getting efficient bill summary: $e');
      // Return default summary on error
      return {
        'totalCount': 0,
        'totalAmount': 0.0,
        'paidAmount': 0.0,
        'unpaidAmount': 0.0,
        'paidCount': 0,
        'unpaidCount': 0,
      };
    }
  }

  /// Get a bill with correctly calculated payment status based on all customer payments
  static Future<Bill?> getBillWithPaymentStatus(int billId) async {
    if (!isInitialized) await initialize();

    // Get the bill
    Bill? bill = await getBillById(billId);
    if (bill == null) return null;

    // Get all payments for this customer, not just the ones linked to this bill
    // This allows us to allocate payments across multiple bills
    final customerPayments = await getPaymentsByCustomer(bill.customerId);
    // Process payments for this bill

    // Use the Bill's updatePaymentStatus method for consistency
    bill.updatePaymentStatus(customerPayments);

    return bill;
  }

  // Instance method that calls the static initialize
  Future<void> initializeDatabase({String? accountId}) async {
    await DatabaseService.initialize(accountId: accountId);
  }

  // Check if we're using in-memory storage - always returns false now
  static Future<bool> isUsingInMemoryStorage() async {
    if (!isInitialized) await initialize();
    return false; // We always use the database now
  }

  // This method is kept for backward compatibility but does nothing now
  static Future<void> saveInMemoryDataToStorage() async {
    if (!isInitialized) await initialize();
    // In-memory storage is no longer used
  }

  // This method is kept for backward compatibility but does nothing now
  static Future<void> clearInMemoryData() async {
    if (!isInitialized) await initialize();
    // In-memory storage is no longer used
  }

  // Verify database schema and fix if needed
  static Future<void> _verifyDatabaseSchema([Database? providedDb]) async {
    try {
      final db = providedDb ?? await database;

      // Check if customers table exists
      final List<Map<String, dynamic>> tables = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='customers'");

      if (tables.isEmpty) {
        _logger.w('Customers table does not exist, creating it');
        await db.execute('''
          CREATE TABLE customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            contactNumber TEXT,
            createdAt TEXT NOT NULL,
            balance REAL NOT NULL DEFAULT 0.0,
            accountId TEXT
          )
        ''');
        await db.execute('CREATE INDEX idx_customers_name ON customers (name)');
        _logger.i('Customers table created successfully');
        return;
      }

      // Check if balance column exists in customers table
      final List<Map<String, dynamic>> tableInfo =
          await db.rawQuery("PRAGMA table_info(customers)");

      final bool hasBalanceColumn =
          tableInfo.any((column) => column['name'] == 'balance');

      if (!hasBalanceColumn) {
        _logger.w(
            'Balance column missing in customers table, attempting to add it');
        try {
          await db.execute(
              'ALTER TABLE customers ADD COLUMN balance REAL NOT NULL DEFAULT 0.0');
          _logger.i('Balance column added successfully');

          // Update all existing customers to have a balance of 0.0
          await db.update('customers', {'balance': 0.0});
          _logger.i('Updated all existing customers with default balance');
        } catch (e) {
          _logger.e('Error adding balance column: $e');

          // If alter table fails, we need a more drastic approach - backup and recreate
          await _recreateCustomersTable(db);
        }
      }
    } catch (e) {
      _logger.e('Error verifying database schema: $e');
      // Try to recover by recreating the table if possible
      try {
        final db = providedDb ?? await database;
        await _recreateCustomersTable(db);
      } catch (recreateError) {
        _logger.e(
            'Failed to recover from schema verification error: $recreateError');
      }
    }
  }

  // Recreate customers table with proper schema
  static Future<void> _recreateCustomersTable(Database db) async {
    _logger.w('Attempting to recreate customers table with proper schema');

    try {
      // Begin transaction for safety
      await db.transaction((txn) async {
        List<Map<String, dynamic>> existingCustomers = [];

        // 1. Check if customers table exists and backup existing customers
        try {
          existingCustomers = await txn.query('customers');
          _logger.i(
              'Successfully backed up ${existingCustomers.length} customers');
        } catch (e) {
          _logger.w('Could not query existing customers: $e');
          // Continue with empty list - we'll create a new table
        }

        // 2. Check if customers_new table already exists (from a previous failed attempt)
        try {
          await txn.execute('DROP TABLE IF EXISTS customers_new');
          _logger
              .i('Dropped existing customers_new table from previous attempt');
        } catch (e) {
          _logger.w('Error dropping customers_new table: $e');
          // Continue anyway
        }

        // 3. Create a temporary table with correct schema
        await txn.execute('''
          CREATE TABLE customers_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            contactNumber TEXT,
            createdAt TEXT NOT NULL,
            balance REAL NOT NULL DEFAULT 0.0,
            accountId TEXT
          )
        ''');
        _logger.i('Created new customers_new table with correct schema');

        // 4. Copy data to new table if we have existing customers
        if (existingCustomers.isNotEmpty) {
          for (final customer in existingCustomers) {
            try {
              final Map<String, dynamic> newCustomer = {
                'id': customer['id'],
                'name': customer['name'] ?? 'Unknown',
                'contactNumber': customer['contactNumber'],
                'createdAt':
                    customer['createdAt'] ?? DateTime.now().toIso8601String(),
                'balance': 0.0, // Default balance
                'accountId': customer['accountId']
              };

              await txn.insert('customers_new', newCustomer);
            } catch (e) {
              _logger.w('Error copying customer ${customer['id']}: $e');
              // Continue with next customer
            }
          }
          _logger.i('Copied customer data to new table');
        }

        // 5. Drop old table if it exists
        try {
          await txn.execute('DROP TABLE IF EXISTS customers');
          _logger.i('Dropped old customers table');
        } catch (e) {
          _logger.e('Error dropping customers table: $e');
          // This is critical, but try to continue
        }

        // 6. Rename new table to customers
        await txn.execute('ALTER TABLE customers_new RENAME TO customers');
        _logger.i('Renamed customers_new to customers');

        // 7. Recreate indexes
        await txn
            .execute('CREATE INDEX idx_customers_name ON customers (name)');
        _logger.i('Recreated indexes on customers table');
      });

      _logger.i('Successfully recreated customers table with balance column');
    } catch (e) {
      _logger.e('Error recreating customers table: $e');
      throw Exception('Failed to recreate customers table: $e');
    }
  }

  // Ensure expenses table exists and has the required indexes
  static Future<void> _ensureExpensesTableExists(Database db) async {
    // Create the table if it doesn't exist
    await db.execute('''
      CREATE TABLE IF NOT EXISTS expenses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        accountId INTEGER,
        description TEXT,
        amount REAL,
        date TEXT,
        category TEXT,
        paymentMethod TEXT,
        remarks TEXT
      )
    ''');

    // OPTIMIZATION: Add indexes to improve query performance
    try {
      // Index for accountId which is used in all queries
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_expenses_accountId ON expenses (accountId)',
      );

      // Index for date sorting which is used frequently
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses (date DESC)',
      );

      // Index for category filtering
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses (category)',
      );

      // Composite index for common filtering scenarios
      await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_expenses_account_date ON expenses (accountId, date DESC)',
      );
    } catch (e) {
      // Indexes might already exist or couldn't be created
      // In production, log this error appropriately
    }
  }

  static Future<List<Expense>> getAllExpenses({
    int offset = 0,
    int limit = 20,
    String? searchQuery,
    String? category,
    DateTime? startDate,
    DateTime? endDate,
    String? paymentMethod,
  }) async {
    if (!isInitialized) await initialize();

    final db = await database;

    try {
      // Ensure expenses table exists with optimized indexes
      await _ensureExpensesTableExists(db);

      String whereClause = 'accountId = ? OR accountId IS NULL';
      List<dynamic> whereArgs = [_currentAccountId];

      if (searchQuery != null && searchQuery.isNotEmpty) {
        whereClause += ' AND (description LIKE ? OR category LIKE ?)';
        whereArgs.add('%$searchQuery%');
        whereArgs.add('%$searchQuery%');
      }

      if (category != null && category.isNotEmpty) {
        whereClause += ' AND category = ?';
        whereArgs.add(category);
      }

      if (startDate != null) {
        whereClause += ' AND date >= ?';
        whereArgs.add(startDate.toIso8601String());
      }

      if (endDate != null) {
        whereClause += ' AND date <= ?';
        whereArgs.add(endDate.toIso8601String());
      }

      if (paymentMethod != null && paymentMethod.isNotEmpty) {
        whereClause += ' AND paymentMethod LIKE ?';
        whereArgs.add('%$paymentMethod%');
      }

      // OPTIMIZATION: Use transaction for better performance
      return await db.transaction((txn) async {
        final List<Map<String, dynamic>> maps = await txn.query(
          'expenses',
          where: whereClause,
          whereArgs: whereArgs,
          orderBy: 'date DESC',
          limit: limit,
          offset: offset,
        );

        return List.generate(maps.length, (i) {
          return Expense.fromMap(maps[i]);
        });
      });
    } catch (e) {
      // If any error occurs, return empty list
      return [];
    }
  }

  /// Get recent expenses
  ///
  /// Returns the most recent expenses, limited by the specified count.
  static Future<List<Expense>> getRecentExpenses({int limit = 5}) async {
    return getAllExpenses(limit: limit);
  }

  /// Get expenses grouped by category
  ///
  /// [startDate]: Optional start date for filtering.
  /// [endDate]: Optional end date for filtering.
  /// Returns a `Future` that completes with a map where keys are category names
  /// and values are the total expenses for that category.
  static Future<Map<String, double>> getExpensesByCategory({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    if (!isInitialized) await initialize();

    final db = await database;
    final Map<String, double> result = {};

    try {
      // Build the WHERE clause for date filtering
      List<String> whereConditions = ['(accountId = ? OR accountId IS NULL)'];
      List<dynamic> whereArgs = [_currentAccountId];

      if (startDate != null) {
        whereConditions.add('date >= ?');
        whereArgs.add(startDate.toIso8601String());
      }

      if (endDate != null) {
        whereConditions.add('date <= ?');
        whereArgs.add(endDate.toIso8601String());
      }

      final whereClause = whereConditions.join(' AND ');

      // Get all expenses matching the criteria
      final List<Map<String, dynamic>> maps = await db.query(
        'expenses',
        where: whereClause,
        whereArgs: whereArgs,
      );

      // Group expenses by category and sum amounts
      for (var map in maps) {
        final expense = Expense.fromMap(map);
        // Category is non-nullable in the Expense class
        final String category = expense.category;
        final currentAmount = result[category];
        result[category] = (currentAmount ?? 0.0) + expense.amount;
      }

      return result;
    } catch (e) {
      _logger.e('Error getting expenses by category: $e');
      return {};
    }
  }

  /// Get expense summary data
  ///
  /// [startDate]: Optional start date for filtering.
  /// [endDate]: Optional end date for filtering.
  /// Returns a `Future` that completes with a map containing summary data.
  static Future<Map<String, double>> getExpenseSummary({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    if (!isInitialized) await initialize();

    final db = await database;

    try {
      // Build the WHERE clause for date filtering
      List<String> whereConditions = ['(accountId = ? OR accountId IS NULL)'];
      List<dynamic> whereArgs = [_currentAccountId];

      if (startDate != null) {
        whereConditions.add('date >= ?');
        whereArgs.add(startDate.toIso8601String());
      }

      if (endDate != null) {
        whereConditions.add('date <= ?');
        whereArgs.add(endDate.toIso8601String());
      }

      final whereClause = whereConditions.join(' AND ');

      // Use a single efficient query to get summary data
      final results = await db.rawQuery('''
        SELECT
          COUNT(*) as count,
          SUM(amount) as totalAmount,
          AVG(amount) as averageAmount,
          MAX(amount) as maxAmount,
          MIN(amount) as minAmount
        FROM expenses
        WHERE $whereClause
      ''', whereArgs);

      if (results.isNotEmpty) {
        final row = results.first;
        return {
          'count': (row['count'] as int?)?.toDouble() ?? 0.0,
          'totalAmount': row['totalAmount'] as double? ?? 0.0,
          'averageAmount': row['averageAmount'] as double? ?? 0.0,
          'maxAmount': row['maxAmount'] as double? ?? 0.0,
          'minAmount': row['minAmount'] as double? ?? 0.0,
        };
      }

      return {
        'count': 0.0,
        'totalAmount': 0.0,
        'averageAmount': 0.0,
        'maxAmount': 0.0,
        'minAmount': 0.0,
      };
    } catch (e) {
      _logger.e('Error getting expense summary: $e');
      return {
        'count': 0.0,
        'totalAmount': 0.0,
        'averageAmount': 0.0,
        'maxAmount': 0.0,
        'minAmount': 0.0,
      };
    }
  }

  /// Get account summary (income, expenses, net balance)
  ///
  /// [startDate]: Optional start date for filtering.
  /// [endDate]: Optional end date for filtering.
  /// Returns a `Future` that completes with a map containing financial summary figures.
  static Future<Map<String, double>> getAccountSummary({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    if (!isInitialized) await initialize();

    try {
      // Get total income (payments)
      double totalIncome = 0.0;
      double totalExpenses = 0.0;

      // Build date conditions for payments
      List<String> paymentConditions = ['(accountId = ? OR accountId IS NULL)'];
      List<dynamic> paymentArgs = [_currentAccountId];

      if (startDate != null) {
        paymentConditions.add('paymentDate >= ?');
        paymentArgs.add(startDate.toIso8601String());
      }

      if (endDate != null) {
        paymentConditions.add('paymentDate <= ?');
        paymentArgs.add(endDate.toIso8601String());
      }

      final paymentWhereClause = paymentConditions.join(' AND ');

      // Get total payments
      final db = await database;
      final paymentResults = await db.rawQuery('''
        SELECT SUM(amount) as totalAmount
        FROM payments
        WHERE $paymentWhereClause
      ''', paymentArgs);

      if (paymentResults.isNotEmpty &&
          paymentResults.first['totalAmount'] != null) {
        totalIncome = paymentResults.first['totalAmount'] as double? ?? 0.0;
      }

      // Get total expenses
      final expenseSummary = await getExpenseSummary(
        startDate: startDate,
        endDate: endDate,
      );

      totalExpenses = expenseSummary['totalAmount'] ?? 0.0;

      // Calculate net balance
      final netBalance = totalIncome - totalExpenses;

      return {
        'income': totalIncome,
        'expenses': totalExpenses,
        'netBalance': netBalance,
      };
    } catch (e) {
      _logger.e('Error getting account summary: $e');
      return {
        'income': 0.0,
        'expenses': 0.0,
        'netBalance': 0.0,
      };
    }
  }

  /// Get recent bills
  ///
  /// Returns the most recent bills, limited by the specified count.
  static Future<List<Bill>> getRecentBills({int limit = 5}) async {
    if (!isInitialized) await initialize();

    final db = await database;

    try {
      final List<Map<String, dynamic>> maps = await db.query(
        'bills',
        where: 'accountId = ? OR accountId IS NULL',
        whereArgs: [_currentAccountId],
        orderBy: 'billDate DESC',
        limit: limit,
      );

      return List.generate(maps.length, (i) {
        return Bill.fromMap(maps[i]);
      });
    } catch (e) {
      // If any error occurs, return empty list
      return [];
    }
  }

  /// Get recent payments
  ///
  /// Returns the most recent payments, limited by the specified count.
  static Future<List<Payment>> getRecentPayments({int limit = 5}) async {
    if (!isInitialized) await initialize();

    final db = await database;

    try {
      final List<Map<String, dynamic>> maps = await db.query(
        'payments',
        where: 'accountId = ? OR accountId IS NULL',
        whereArgs: [_currentAccountId],
        orderBy: 'paymentDate DESC',
        limit: limit,
      );

      return List.generate(maps.length, (i) {
        return Payment.fromMap(maps[i]);
      });
    } catch (e) {
      // If any error occurs, return empty list
      return [];
    }
  }

  static Future<int> saveExpense(Expense expense) async {
    if (!isInitialized) await initialize();

    final db = await database;

    try {
      // Ensure expenses table exists with optimized indexes
      await _ensureExpensesTableExists(db);

      // Add the current account ID to the expense data
      final expenseMap = expense.toMap();
      expenseMap['accountId'] = _currentAccountId;

      // OPTIMIZATION: Use transaction for better performance
      return await db.transaction((txn) async {
        // Use raw insert for better performance on large datasets
        final id = await txn.rawInsert(
          '''INSERT INTO expenses(
            accountId, description, amount, date, category, paymentMethod, remarks
          ) VALUES(?, ?, ?, ?, ?, ?, ?)''',
          [
            _currentAccountId,
            expense.description,
            expense.amount,
            expense.date.toIso8601String(),
            expense.category,
            expense.paymentMethod,
            expense.remarks,
          ],
        );

        // By returning the ID immediately, we allow the UI to update faster
        return id;
      });
    } catch (e) {
      // If any error occurs, return 0 (invalid ID)
      return 0;
    }
  }

  static Future<Expense?> getExpenseById(int id) async {
    if (!isInitialized) await initialize();

    final db = await database;

    try {
      // Ensure expenses table exists
      await _ensureExpensesTableExists(db);

      final List<Map<String, dynamic>> maps = await db.query(
        'expenses',
        where: 'id = ? AND (accountId = ? OR accountId IS NULL)',
        whereArgs: [id, _currentAccountId],
      );

      if (maps.isEmpty) {
        return null;
      }

      return Expense.fromMap(maps.first);
    } catch (e) {
      return null;
    }
  }

  static Future<bool> deleteExpense(int id) async {
    if (!isInitialized) await initialize();

    final db = await database;

    try {
      // Ensure expenses table exists
      await _ensureExpensesTableExists(db);

      return await db.transaction((txn) async {
        final result = await txn.delete(
          'expenses',
          where: 'id = ? AND (accountId = ? OR accountId IS NULL)',
          whereArgs: [id, _currentAccountId],
        );

        return result > 0;
      });
    } catch (e) {
      return false;
    }
  }

  static Future<int> updateExpense(Expense expense) async {
    if (!isInitialized) await initialize();

    final db = await database;

    try {
      // Ensure expenses table exists
      await _ensureExpensesTableExists(db);

      // Add the current account ID to the expense data
      final expenseMap = expense.toMap();
      expenseMap['accountId'] = _currentAccountId;

      return await db.transaction((txn) async {
        return await txn.update(
          'expenses',
          expenseMap,
          where: 'id = ? AND (accountId = ? OR accountId IS NULL)',
          whereArgs: [expense.id, _currentAccountId],
        );
      });
    } catch (e) {
      return 0;
    }
  }

  static Future<Map<String, num>> getBillsSummary({
    DateTime? startDate,
    DateTime? endDate,
    bool? isPaid,
    int? customerId,
    String? searchQuery,
  }) async {
    if (!isInitialized) await initialize();

    // Build the WHERE clause
    List<String> whereConditions = [];
    List<dynamic> whereArgs = [];

    // Always filter by account ID
    whereConditions.add('(accountId = ? OR accountId IS NULL)');
    whereArgs.add(_currentAccountId);

    if (customerId != null) {
      whereConditions.add('customerId = ?');
      whereArgs.add(customerId);
    }

    if (isPaid != null) {
      whereConditions.add('isPaid = ?');
      whereArgs.add(isPaid ? 1 : 0);
    }

    if (startDate != null) {
      whereConditions.add('billDate >= ?');
      whereArgs.add(startDate.toIso8601String());
    }

    if (endDate != null) {
      final endDatePlusOne = endDate.add(const Duration(days: 1));
      whereConditions.add('billDate < ?');
      whereArgs.add(endDatePlusOne.toIso8601String());
    }

    String whereClause = whereConditions.join(' AND ');

    // Get the bills
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'bills',
      where: whereClause.isEmpty ? null : whereClause,
      whereArgs: whereArgs.isEmpty ? null : whereArgs,
    );

    List<Bill> bills = List.generate(maps.length, (i) {
      return Bill.fromMap(maps[i]);
    });

    // Apply search query filter if needed
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final lowercaseQuery = searchQuery.toLowerCase();

      // Get customer names for searching
      final customerIds = bills.map((bill) => bill.customerId).toSet().toList();

      if (customerIds.isNotEmpty) {
        // Create placeholders for the IN clause
        final placeholders =
            List.generate(customerIds.length, (index) => '?').join(',');

        final List<Map<String, dynamic>> customerMaps = await db.query(
          'customers',
          where: 'id IN ($placeholders)',
          whereArgs: customerIds,
        );

        final customers = List.generate(customerMaps.length, (i) {
          return Customer.fromMap(customerMaps[i]);
        });

        final customersMap = {for (var c in customers) c.id: c};

        // Filter bills by search query
        bills = bills.where((bill) {
          final customer = customersMap[bill.customerId];
          final customerName = customer?.name.toLowerCase() ?? '';
          final billId = bill.id.toString();
          final amount = bill.amount.toString();

          return customerName.contains(lowercaseQuery) ||
              billId.contains(lowercaseQuery) ||
              amount.contains(lowercaseQuery);
        }).toList();
      }
    }

    // Calculate summary
    int totalCount = bills.length;
    double totalAmount = bills.fold(0.0, (sum, bill) => sum + bill.amount);

    // Calculate paid amount (including fully and partially paid bills)
    double paidAmount = 0.0;
    for (var bill in bills) {
      if (bill.isPaid) {
        paidAmount += bill.amount;
      } else if (bill.isPartiallyPaid && bill.partialAmount != null) {
        paidAmount += bill.partialAmount!;
      }
    }

    // Calculate unpaid amount (including remaining balance on partially paid bills)
    double unpaidAmount = totalAmount - paidAmount;

    int paidCount = bills.where((bill) => bill.isPaid).length;
    int unpaidCount = bills.where((bill) => !bill.isPaid).length;

    return {
      'totalCount': totalCount,
      'totalAmount': totalAmount,
      'paidAmount': paidAmount,
      'unpaidAmount': unpaidAmount,
      'paidCount': paidCount,
      'unpaidCount': unpaidCount,
    };
  }
}
